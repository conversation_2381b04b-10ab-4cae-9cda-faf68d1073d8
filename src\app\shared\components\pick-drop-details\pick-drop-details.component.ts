import { Component, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>iew<PERSON>hecked, AfterViewInit } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';
import { CityRequest } from '../../models/city-request.model';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subscription } from 'rxjs';
import { MmiService } from '../../services/mmi/mmi-service.service';
import { GoogleMapsService } from '../../services/google-maps/google-maps.service';
import { HttpErrorResponse } from '@angular/common/http';
import { FormControl } from '@angular/forms';
import { map, startWith, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { MMISuggestedModel } from '../../models/mmi/mmi_suggested_locations_model';
import { SuggestedLocation } from '../../models/suggested-booking-categories.model';
import { AppConstants } from '../../constants/AppConstants';
import { PolyLineDecoderService } from '../../services/poyline-decoder.service';
import { ETA, Route } from '../../models/mmi/mmt_eta_model';
import { LocationCoordinate } from '../../models/location_coordinate_model';
import { RouteInfo } from '../../models/route_info.model';
import { ValidationMessages, ErrorMessage } from '../../constants/message.content';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { ProgressService } from '../../services/progress.service';
import { PickUpDropOffRequest } from '../../models/pick-drop-request.model';
import { CarCategoryDetails, FareDistanceCalculateResponse } from '../../models/car-category-fare-details';
import { LoggerService } from '../../interceptors/logger.service';
import { HomeApiService } from '../../services/home-api.service';
import { GenericAPIArrayResponse } from '../../models/generic-api-response.model';
import { NearByCity } from '../../models/city_model';

declare var google: any;
declare var $: any;

@Component({
  selector: 'app-pick-drop-details',
  templateUrl: './pick-drop-details.component.html',
  styleUrls: ['./pick-drop-details.component.css']
})
export class PickDropDetailsComponent implements OnInit, AfterViewChecked, AfterViewInit, OnDestroy {

  constructor(
    private mmiService: MmiService,
    private googleMapsService: GoogleMapsService,
    private toastrService: ToastrService,
    private polyLineDecoderService: PolyLineDecoderService,
    private progresService: ProgressService,
    private homeAPIService: HomeApiService,
    private router: Router,
    private route: ActivatedRoute) {
    this.logger = LoggerService.createLogger('PickDropDetailsComponent');
    this.logger.debug('Inside Constructor');
  }

  private logger: LoggerService;

  private apiCounter: number;

  public map: any;
  private token: string;
  private routePolyline: any;
  private routeInfoWindow: any;

  routeInfo: RouteInfo;

  imageFolderPath: string = AppConfig.imageFolderPath;

  carCategoryFareList: FareDistanceCalculateResponse[] = [];

  selectedCarCategoryIndex: number;
  private selectedCarCategory: CarCategoryDetails;

  /**
   * This is the city request we form using
   * the url path param by calling API response
   * e.g., Delhi-to-Jaipur
   * */
  private cityRequest: CityRequest;

  // This is the pickup dropff request that will be used for booking
  private pickupDropoffRequest: PickUpDropOffRequest;

  filteredOptionsPickUpCityAddress: Observable<string[]>;
  filteredOptionsDropOffCityAddress: Observable<string[]>;

  pickUpCityAddressControl = new FormControl();
  dropOffCityAddressControl = new FormControl();
  pickUpTimeControl = new FormControl();
  pickUpDateControl = new FormControl();

  pickUpAddressCityMap: Map<string, SuggestedLocation>;
  dropOffAddressCityMap: Map<string, SuggestedLocation>;

  private etaSubscription: Subscription;
  private carCategorySubscription: Subscription;

  private startMarker;
  private endMarker;
  private routePolyline;

  // Custom picker properties
  private activePicker: any = null;

  ngOnInit() {
    this.apiCounter = 0;

    this.subscribeTravelCitiesPathParam();

    this.initGoogleMap();

    this.mmiService.getToken().then((data) => {
      this.token = data['access_token'];
      this.logger.debug('response data', this.token);
    });

    this.filteredOptionsPickUpCityAddress = this.pickUpCityAddressControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(AppConstants.AUTO_SUGGEST_DEBOUNCE_MILLSECONDS),
        map(value => this._filterPickUpAddressCity(value))
      );

    this.filteredOptionsDropOffCityAddress = this.dropOffCityAddressControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(AppConstants.AUTO_SUGGEST_DEBOUNCE_MILLSECONDS),
        map(value => this._filterDropOffAddressCity(value))
      );
  }

  ngAfterViewInit() {
    this.initCustomDatePicker();
    this.initCustomTimePicker();
  }

  ngAfterViewChecked() {
    // Never print logs here it will print cotinuosly in console
    // this.logger.trace('ngAfterViewChecked() called');
    this.initToolTip();
  }

  private initToolTip = () => {
    // Never print logs here it will print cotinuosly in console as it is called from ngAfterViewChecked
    // this.logger.trace('initToolTip() called');
    $('[data-toggle="tooltip"]').tooltip();
  }

  private resetPage = () => {
    this.logger.trace('resetPage() called');
    this.pickupDropoffRequest = new PickUpDropOffRequest();

    this.carCategoryFareList = [];
    this.pickUpDateControl.reset();
    this.pickUpTimeControl.reset();

    this.removeSourceMarker();
    this.removeDestinationMarker();
    this.removeMapPolylineTooltip();
    this.removeMapPolyline();

    this.pickUpCityAddressControl.reset();
    this.dropOffCityAddressControl.reset();

    this.resetAddressMaps();
    this.resetRouteInfo();
  }

  private subscribeTravelCitiesPathParam = () => {
    this.logger.trace('subscribeTravelCitiesPathParam() called');
    this.route.paramMap
      .pipe(distinctUntilChanged())
      .subscribe(paramMap => {
      this.logger.trace('listened to value in subscribeTravelCitiesPathParam', paramMap);

      this.resetPage();
      this.readTravelCitiesPathParam();
    });
  }

  private readTravelCitiesPathParam = () => {
    this.logger.trace('readTravelCitiesPathParam() called');
    const travelCities = this.route.snapshot.paramMap.get('travelcities');

    this.logger.trace('travelCities', travelCities);
    if (travelCities && travelCities.trim().length > 0) {
      this.getPickupDropOffCityFromAPI(travelCities);
    } else {
      this.logger.trace('pathParam not present in route url');
      this.navigateHomePage();
    }
  }

  private getPickupDropOffCityFromAPI = (travelCities: string) => {
    this.logger.trace('getPickupDropOffCityFromAPI() called with travelCities: ', travelCities);

    const array = travelCities.split('-to-');
    const pickUpCity = array[0];
    const dropOffCity = array[1];
    this.logger.trace('pickUpCity', pickUpCity, 'dropOffCity', dropOffCity);

    if (!pickUpCity || pickUpCity.trim().length === 0) {
      this.logger.trace('pickUpCity not present in route url');
      this.navigateHomePage();
      return;
    }

    if (!dropOffCity || dropOffCity.trim().length === 0) {
      this.logger.trace('dropOffCity not present in route url');
      this.navigateHomePage();
      return;
    }

    this.showProgressBar();
    this.homeAPIService.getCityInfo(pickUpCity, dropOffCity).subscribe(
      (response: GenericAPIArrayResponse<NearByCity>) => {
        this.hideProgressBar();
        this.logger.trace('getCityInfo() response', response);
        this.parseGetCityInfoAPIResponse(
          response,
          pickUpCity,
          dropOffCity);
      },
      (error) => {
        this.hideProgressBar();
        this.logger.error('getCityInfo() API Error', error);
        this.navigateHomePage();
      }
    );
  }

  private parseGetCityInfoAPIResponse = (
    response: GenericAPIArrayResponse<NearByCity>,
    pickUpCity: string,
    dropOffCity: string) => {
    this.logger.trace('parseGetCityInfoAPIResponse() called with response', response,
      'pickUpCity', pickUpCity,
      'dropOffCity', dropOffCity);

    if (!response.succeeded) {
      this.logger.debug('Success flag not found in getCityInfo() API Response');
      this.navigateHomePage();
      return;
    }

    const cities = response.data;
    this.logger.trace('cities', cities);
    if (!cities || cities.length < 2) {
      this.logger.trace('cities insuffient in API response');
      this.navigateHomePage();
      return;
    }

    const source = cities.find(city => city.cityName === pickUpCity);
    const destination = cities.find(city => city.cityName === dropOffCity);
    this.createCityRequest(source, destination);

    this.fitMapBoundToCities();
  }

  private fitMapBoundToCities = () => {
    this.logger.trace('fitMapBoundToCities() called');

    const srcLat = +this.cityRequest.pickUpCityLatitutde;
    const srcLng = +this.cityRequest.pickUpCityLongituted;
    const dstLat = +this.cityRequest.dropOffCityLatitutde;
    const dstLng = +this.cityRequest.dropOffCityLongituted;
    this.logger.trace('srcLat', srcLat, 'srcLng', srcLng, 'dstLat', dstLat, 'dstLng', dstLng);

    const bounds = new L.LatLngBounds([srcLat, srcLng], [dstLat, dstLng]);
    this.map.fitBounds(bounds);
  }

  private createCityRequest = (source: NearByCity, destination: NearByCity) => {
    this.logger.trace('createCityRequest() called with source', source, 'destination', destination);
    this.cityRequest = new CityRequest();

    this.cityRequest.pickUpCity = source.cityName;
    this.cityRequest.pickUpCityEloc  = source.eloc;
    this.cityRequest.pickUpCityLatitutde = source.latitude;
    this.cityRequest.pickUpCityLongituted = source.longitude;

    this.cityRequest.dropOffCity = destination.cityName;
    this.cityRequest.dropOffCityEloc = destination.eloc;
    this.cityRequest.dropOffCityLatitutde = destination.latitude;
    this.cityRequest.dropOffCityLongituted = destination.longitude;

    this.logger.trace('formed cityRequest', this.cityRequest);
  }

  private navigateHomePage = () => {
    this.logger.trace('navigateHomePage() called');
    this.router.navigate(['/home']);
  }

  ngOnDestroy() {
    this.unsubscribeSubscriptions();
  }

  private unsubscribeSubscriptions = () => {
    if (this.etaSubscription) {
      this.etaSubscription.unsubscribe();
    }
    if (this.carCategorySubscription) {
      this.carCategorySubscription.unsubscribe();
    }
  }

  private initGoogleMap = () => {
    if (typeof google !== 'undefined' && google.maps) {
      this.map = new google.maps.Map(document.getElementById('map'), {
        center: { lat: 28.04, lng: 78.2 }, // Delhi coordinates
        zoom: 12,
        mapTypeId: google.maps.MapTypeId.ROADMAP
      });
    } else {
      // Retry after a short delay if Google Maps is not loaded yet
      setTimeout(() => this.initGoogleMap(), 100);
    }
  }

  private resetRouteInfo = () => {
    this.logger.trace('resetRouteInfo() called');
    this.routeInfo = null;
  }

  public selectCategoryIndex = (index: number) => {
    this.logger.trace('selectCategoryIndex() called with index index', index);
    const carCategory = this.carCategoryFareList[index];
    this.selectedCarCategory = carCategory['carCategoryList'];
    this.selectedCarCategoryIndex = index;
    this.logger.trace('selectedCarCategory', this.selectedCarCategory, 'selectedCarCategoryIndex', this.selectedCarCategoryIndex);
  }

  private recalculateRouteInfo = (route: Route) => {
    this.logger.trace('recalculateRouteInfo() called with route', route);

    const distance = route.distance;
    const duration = route.duration;
    this.logger.trace('distance', distance, 'duration', duration);

    // Because duration is in seconds
    const formattedDuration = this.millisecondsToTime(duration * 1000);
    const formaatedDistance = this.metresToKms(distance);

    this.routeInfo = new RouteInfo();
    this.routeInfo.distance = distance;
    this.routeInfo.duration = duration;
    this.routeInfo.distanceString = formaatedDistance;
    this.routeInfo.durationString = formattedDuration;
    this.logger.trace('recalculateRouteInfo() changed routeInfo to ', this.routeInfo);
  }

  private millisecondsToTime = (duration: number): string => {
    this.logger.trace('millisecondsToTime() called with duration', duration);
    const seconds = Math.floor((duration / 1000) % 60);
    const minutes = Math.floor((duration / (1000 * 60)) % 60);
    const hours = Math.floor((duration / (1000 * 60 * 60)) % 24);
    this.logger.trace('hours', hours, 'minutes', minutes, 'seconds', seconds);

    const hoursString = (hours < 10) ? '0' + hours : hours;
    const minuteString = (minutes < 10) ? '0' + minutes : minutes;
    const secondsString = (seconds < 10) ? '0' + seconds : seconds;
    this.logger.trace('hoursString', hoursString, 'minuteString', minuteString, 'secondsString', secondsString);

    const result = hoursString + ' hr(s) ' + minuteString + ' min(s)';
    this.logger.trace('millisecondsToTime() returning result', result);
    return result;
  }

  private metresToKms = (distance: number): string => {
    let kms = (distance / 1000);
    // To round off to 2 decimal places
    kms = Math.round(kms * 100) / 100;

    const result = kms.toString() + ' kms';
    this.logger.trace('metresToKms() returning result', result);
    return result;
  }

  private resetAddressMaps = () => {
    this.resetPickupAddressMap();
    this.resetDropOffAddressMap();
  }

  private resetPickupAddressMap = () => {
    this.pickUpAddressCityMap = new Map();
  }

  private resetDropOffAddressMap = () => {
    this.dropOffAddressCityMap = new Map();
  }

  private setPickupAddressMap = (location: SuggestedLocation) => {
    this.pickUpAddressCityMap.set(location.placeName + ', ' + location.placeAddress, location);
  }

  private setDropOffAddressMap = (location: SuggestedLocation) => {
    this.dropOffAddressCityMap.set(location.placeName + ', ' + location.placeAddress, location);
  }

  private isValidForAutoSuggest = (value: string): boolean => {
    this.logger.trace('isValidForAutoSuggest called with value', value);
    if (!value) {
      this.logger.trace('Value is invalid');
      return false;
    }

    const flag = (value.length >= AppConstants.AUTO_SUGGEST_LIMIT);
    this.logger.trace('value.length: ', value.length, ' flag: ', flag);
    return flag;
  }

  private _filterPickUpAddressCity(value: string): string[] {
    this.resetPickupAddressMap();
    const pickUpAddressCity: string[] = [];

    // const filterValue = value.toLowerCase();
    const filterValue = value;

    if (!this.isValidForAutoSuggest(filterValue)) {
      this.logger.trace('Not valid for autosuugest for pickup');
      return;
    }

    // Use Google Maps autocomplete instead of MMI
    this.googleMapsService.autoSuggest(filterValue).subscribe(
      (response: any) => {
        if (response && response.suggestedLocations) {
              const suggestedLocations = response.suggestedLocations;
              if (suggestedLocations) {
                // const pickUpCityAddressNames = suggestedLocations.filter(option => option.placeName.toLowerCase().includes(filterValue));
                suggestedLocations.forEach(element => {
                  pickUpAddressCity.push(element.placeName + ', ' + element.placeAddress);
                  this.setPickupAddressMap(element);
                });
              }
            }
          }, (error: HttpErrorResponse) => {
            this.logger.trace(error.error);
          }

        );
    }

    return pickUpAddressCity;
  }

  public _filterDropOffAddressCity(value: string): string[] {
    this.resetDropOffAddressMap();
    const dropOffAddressCity: string[] = [];

    // const filterValue = value.toLowerCase();
    const filterValue = value;

    if (!this.isValidForAutoSuggest(filterValue)) {
      this.logger.trace('Not valid for autosggest for drop off');
      return;
    }

    // Use Google Maps autocomplete instead of MMI
    this.googleMapsService.autoSuggest(filterValue).subscribe(
      (response: any) => {
        if (response && response.suggestedLocations) {
              const suggestedLocations = response.suggestedLocations;
              if (suggestedLocations) {
                // const dropOffAddressCityNames = suggestedLocations.filter(option => option.placeName.toLowerCase().includes(filterValue));
                suggestedLocations.forEach(element => {
                  dropOffAddressCity.push(element.placeName + ', ' + element.placeAddress);
                  this.setDropOffAddressMap(element);
                });
              }
            }
          }, (error: HttpErrorResponse) => {
            this.logger.trace(error.error);
          }
        );
    }

    return dropOffAddressCity;
  }

  public _filterPickUpCityAddressLatLong(placeAddress: string): void {
    this.logger.trace('_filterPickUpCityAddressLatLong() called with placeAddress', placeAddress);
    const location = this.pickUpAddressCityMap.get(placeAddress);

    this.pickupDropoffRequest.pickUpCity = this.cityRequest.pickUpCity;
    this.pickupDropoffRequest.pickUpCityLatitutde = this.cityRequest.pickUpCityLatitutde;
    this.pickupDropoffRequest.pickUpCityLongituted = this.cityRequest.pickUpCityLongituted;
    this.pickupDropoffRequest.pickUpCityEloc = this.cityRequest.pickUpCityEloc;

    this.pickupDropoffRequest.pickUpAddress = placeAddress;
    this.pickupDropoffRequest.pickUpAddressLatitutde = location.latitude.toString();
    this.pickupDropoffRequest.pickUpAddressLongituted = location.longitude.toString();

    this.callRouteAndCarCategoryAPI();
    this.createNewSourceMarker();
    this.fitMarkersMapBound();
  }

  public _filterDropOffCityAddressLatLong(placeAddress: string): void {
    this.logger.trace('_filterDropOffCityAddressLatLong() called with placeAddress', placeAddress);
    const location = this.dropOffAddressCityMap.get(placeAddress);

    this.pickupDropoffRequest.dropOffCity = this.cityRequest.dropOffCity;
    this.pickupDropoffRequest.dropOffCityLatitutde = this.cityRequest.dropOffCityLatitutde;
    this.pickupDropoffRequest.dropOffCityLongituted = this.cityRequest.dropOffCityLongituted;
    this.pickupDropoffRequest.dropOffCityEloc = this.cityRequest.dropOffCityEloc;

    this.pickupDropoffRequest.dropOffAddress = placeAddress;
    this.pickupDropoffRequest.dropOffAddressLatitutde = location.latitude.toString();
    this.pickupDropoffRequest.dropOffAddressLongituted = location.longitude.toString();

    this.callRouteAndCarCategoryAPI();
    this.setNewDestinationMarker();
    this.fitMarkersMapBound();
  }

  private removeSourceMarker = () => {
    this.logger.trace('removeSourceMarker() called');

    this.logger.trace('this.startMarker', this.startMarker);
    if (this.startMarker) {
      this.logger.trace('remove existing start marker');
      this.map.removeLayer(this.startMarker);
    }
  }

  private createNewSourceMarker = () => {
    this.logger.trace('createNewSourceMarker() called');

    this.removeSourceMarker();

    this.logger.trace('adding new start marker');
    const lat: number = +this.pickupDropoffRequest.pickUpAddressLatitutde;
    const lng: number = +this.pickupDropoffRequest.pickUpAddressLongituted;
    const title = 'Source';
    this.logger.trace('lat', lat, ', lng', lng, ', title', title);

    this.startMarker = new L.marker([lat, lng], { title }).addTo(this.map);
    this.startMarker.bindPopup(title, { closeButton: true, autopan: true, zoomAnimation: true });
  }

  private removeDestinationMarker = () => {
    this.logger.trace('removeDestinationMarker() called');

    this.logger.trace('this.endMarker', this.endMarker);
    if (this.endMarker) {
      this.logger.trace('remove existing end marker');
      this.map.removeLayer(this.endMarker);
    }
  }

  private setNewDestinationMarker = () => {
    this.logger.trace('setDestinationMarker() called');

    this.removeDestinationMarker();

    const lat: number = +this.pickupDropoffRequest.dropOffAddressLatitutde;
    const lng: number = +this.pickupDropoffRequest.dropOffAddressLongituted;
    const title = 'Destination';
    this.logger.trace('lat', lat, ', lng', lng, ', title', title);

    this.endMarker = new L.marker([lat, lng], { title }).addTo(this.map);
    this.endMarker.bindPopup(title, { closeButton: true, autopan: true, zoomAnimation: true });
  }

  private fitMarkersMapBound = () => {
    this.logger.trace('fitMarkersMapBound() called');
    this.logger.trace('startmarker', this.startMarker);
    this.logger.trace('endMarker', this.endMarker);

    if (!this.startMarker || !this.endMarker) {
      this.logger.trace('one of the markers is invalid!');

      if (this.startMarker) {
        this.logger.trace('startmarker is valid will pan to that');
        this.fitStartMarker();
      } else if (this.endMarker) {
        this.logger.trace('endMarker is valid will pan to that');
        this.fitEndMarker();
      }

      this.logger.trace('fitMarkersMapBound() returning');
      return;
    }

    const srcLat = +this.pickupDropoffRequest.pickUpAddressLatitutde;
    const srcLng = +this.pickupDropoffRequest.pickUpAddressLongituted;
    const dstLat = +this.pickupDropoffRequest.dropOffAddressLatitutde;
    const dstLng = +this.pickupDropoffRequest.dropOffAddressLongituted;
    this.logger.trace('srcLat', srcLat, 'srcLng', srcLng, 'dstLat', dstLat, 'dstLng', dstLng);

    const bounds = new L.LatLngBounds([srcLat, srcLng], [dstLat, dstLng]);
    this.map.fitBounds(bounds);
  }

  private fitStartMarker = () => {
    this.logger.trace('fitStartMarker() called');
    this.map.panTo(this.startMarker._latlng);
  }

  private fitEndMarker = () => {
    this.logger.trace('fitEndMarker() called');
    this.map.panTo(this.endMarker._latlng);
  }

  private callRouteAndCarCategoryAPI = () => {
    this.logger.trace('callRouteAndCarCategoryAPI() called');
    this.callCarCategoryAPI();
    this.callRouteAPI();
  }

  private callCarCategoryAPI = () => {
    this.logger.trace('callCarCategoryAPI() called');

    this.logger.trace('pickupDropoffRequest', this.pickupDropoffRequest);

    const srcLat = this.pickupDropoffRequest.pickUpCityLatitutde;
    const srcLng = this.pickupDropoffRequest.pickUpCityLongituted;
    const dstLat = this.pickupDropoffRequest.dropOffCityLatitutde;
    const dstLng = this.pickupDropoffRequest.dropOffCityLongituted;

    this.logger.trace('srcLat', srcLat, 'srcLng', srcLng, 'dstLat', dstLat, 'dstLng', dstLng);
    if (!srcLat || !srcLng || !dstLat || !dstLng) {
      this.logger.trace('location coordinates are not present. No need to call Car Category API');
      return;
    }

    const pickUpAddressLonLat = srcLng + ',' + srcLat;
    const dropOffAddressLongLat = dstLng + ',' + dstLat;

    // TODO Don't hard code pick this from menu
    const tripType = 'One Way';

    this.logger.trace('pickUpAddressLonLat', pickUpAddressLonLat,
      'dropOffAddressLongLat', dropOffAddressLongLat,
      'tripType', tripType);

    this.showProgressBar();
    this.carCategorySubscription = this.mmiService.getCarCategoryFare(
      pickUpAddressLonLat,
      dropOffAddressLongLat,
      tripType).subscribe(
        (response) => {
          this.hideProgressBar();
          const isSucesss = response.succeeded;

          if (isSucesss) {
            const data = response.data;
            this.logger.trace('data', data);
            this.carCategoryFareList = data as FareDistanceCalculateResponse[];
            this.logger.trace('carCategoryFareList', this.carCategoryFareList);
          }
        },
        (err) => {
          this.hideProgressBar();
          this.logger.trace(err);
          this.displayCarCategoryAPIError();
        });
  }

  private callRouteAPI = () => {
    this.logger.trace('callRouteAPI() called');

    this.logger.trace('pickupDropoffRequest', this.pickupDropoffRequest);

    const srcLat = this.pickupDropoffRequest.pickUpAddressLatitutde;
    const srcLng = this.pickupDropoffRequest.pickUpAddressLongituted;
    const dstLat = this.pickupDropoffRequest.dropOffAddressLatitutde;
    const dstLng = this.pickupDropoffRequest.dropOffAddressLongituted;

    this.logger.trace('srcLat', srcLat, 'srcLng', srcLng, 'dstLat', dstLat, 'dstLng', dstLng);
    if (!srcLat || !srcLng || !dstLat || !dstLng) {
      this.logger.trace('location coordinates are not present. No need to call Route API');
      return;
    }

    this.showProgressBar();
    this.etaSubscription = this.googleMapsService.getETA(
      srcLat,
      srcLng,
      dstLat,
      dstLng)
      .subscribe(
        (response: ETA) => {
          this.hideProgressBar();

          this.logger.trace('response in getETA() API', response);
          const routes = response.routes;
          this.logger.trace('routes', routes);
          if (routes) {
            const firstRoute = routes[0];
            this.logger.trace('firstRoute', firstRoute);

            if (firstRoute) {
              this.resetRouteInfo();
              this.recalculateRouteInfo(firstRoute);

              const encodedPolyline = firstRoute.geometry;
              const polyline = this.polyLineDecoderService.decode(encodedPolyline);
              this.createNewMapPolyline(polyline);
              this.createNewMapPolylineTooltip();
            } else {
              this.displayRouteAPIError();
            }

          } else {
            this.displayRouteAPIError();
          }
        },
        (err) => {
          this.hideProgressBar();
          this.logger.trace(err);
          this.displayRouteAPIError();
        }
      );
  }

  private removeMapPolyline = () => {
    this.logger.trace('removeMapPolyline() called');

    this.logger.trace('routePolyline', this.routePolyline);
    if (this.routePolyline) {
      this.logger.trace('remove existing route polyline');
      this.routePolyline.setMap(null);
    }
  }

  private removeMapPolylineTooltip = () => {
    this.logger.trace('removeMapPolylineTooltip() called');
    this.logger.trace('routeInfoWindow', this.routeInfoWindow);
    if (this.routeInfoWindow) {
      this.logger.trace('remove existing route info window');
      this.routeInfoWindow.close();
    }
  }

  private createNewMapPolyline = (route: LocationCoordinate[]) => {
    this.logger.trace('createNewMapPolyline() called with route', route);

    this.removeMapPolyline();

    if (typeof google !== 'undefined' && google.maps) {
      const pathArray = [];
      route.forEach(point => pathArray.push(new google.maps.LatLng(point.latitude, point.longitude)));

      this.routePolyline = new google.maps.Polyline({
        path: pathArray,
        geodesic: true,
        strokeColor: '#2196F3',
        strokeOpacity: 1.0,
        strokeWeight: 5
      });

      this.routePolyline.setMap(this.map);

      // Fit map bounds to show the entire route
      const bounds = new google.maps.LatLngBounds();
      pathArray.forEach(point => bounds.extend(point));
      this.map.fitBounds(bounds);
    }
  }

  private createNewMapPolylineTooltip = () => {
    this.logger.trace('createNewMapPolylineTooltip() called');
    this.logger.trace('routeInfo', this.routeInfo);

    this.removeMapPolylineTooltip();

    if (typeof google !== 'undefined' && google.maps && this.routePolyline) {
      const tootTipContent =
      `<div style="padding: 10px; background: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
        <div style="margin-bottom: 5px;">
          <i class="fa fa-car"></i>
            <span class="duration" style="margin-left: 5px; font-weight: bold;">
              ${this.routeInfo.durationString}
            </span>
        </div>
        <div>
          <span class="distance" style="color: #666;">
            ${this.routeInfo.distanceString}
          </span>
        </div>
      </div>`;

      // Get the center point of the polyline for info window placement
      const path = this.routePolyline.getPath();
      const centerIndex = Math.floor(path.getLength() / 2);
      const centerPoint = path.getAt(centerIndex);

      this.routeInfoWindow = new google.maps.InfoWindow({
        content: tootTipContent,
        position: centerPoint
      });

      this.routeInfoWindow.open(this.map);
    }
  }

  private displayRouteAPIError = () => {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.ROUTE_API_ERROR);
  }

  private displayCarCategoryAPIError = () => {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.PICKUP_DROPOFF_CAR_CATEGORY_API_ERROR);
  }

  public validateAndSubmit = () => {
    this.logger.trace('validateAndSubmit() called');

    const flag = this.isValidRequest();
    if (!flag) {
      this.logger.trace('Request is not valid. validateAndSubmit() returning');
      return;
    }

    // Set booking date and time from form controls
    this.pickupDropoffRequest.bookingDate = this.pickUpDateControl.value;
    this.pickupDropoffRequest.bookingTime = this.pickUpTimeControl.value;

    this.logger.trace('pickupDropoffRequest with date/time:', this.pickupDropoffRequest);

    this.navigateBookingPage();
  }

  private isValidRequest = (): boolean => {
    this.logger.trace('isValidRequest() called');

    let flag = this.isPickupLocationValid();
    if (!flag) {
      this.logger.trace('validation failed at isPickupLocationValid');
      return false;
    }

    flag = this.isDropoffLocationValid();
    if (!flag) {
      this.logger.trace('validation failed at isDropoffLocationValid');
      return false;
    }

    flag = this.isSameCityValid();
    if (!flag) {
      this.logger.trace('validation failed at isSameCityValid');
      return false;
    }

    flag = this.isDateTimeValid();
    if (!flag) {
      this.logger.trace('validation failed at isDateTimeValid');
      return false;
    }

    flag = this.isCarCategoryValid();
    if (!flag) {
      this.logger.trace('validation failed at isCarCategoryValid');
      return false;
    }

    this.logger.trace('all validations passed');
    return true;
  }

  private isPickupLocationValid = (): boolean => {
    this.logger.trace('isPickupLocationValid()');
    this.logger.trace('pickupDropoffRequest', this.pickupDropoffRequest);

    if (!this.pickupDropoffRequest.pickUpCity || this.pickupDropoffRequest.pickUpCity.trim().length === 0) {
      this.logger.trace('pickup city not present');
      this.toastrService.error(ValidationMessages.PICK_UP_CITY_ADDRESS, ValidationMessages.INVALID_PICKUP_ADDRESS);
      return false;
    }

    this.logger.trace('pickup city is valid');
    return true;
  }

  private isDropoffLocationValid = (): boolean => {
    this.logger.trace('isDropoffLocationValid()');
    this.logger.trace('pickupDropoffRequest', this.pickupDropoffRequest);

    if (!this.pickupDropoffRequest.dropOffCity || this.pickupDropoffRequest.dropOffCity.trim().length === 0) {
      this.logger.trace('dropoff city not present');
      this.toastrService.error(ValidationMessages.DROP_OFF_CITY_ADDRESS, ValidationMessages.INVALID_DROPOFF_ADDRESS);
      return false;
    }

    this.logger.trace('dropoff city is valid');
    return true;
  }

  private isSameCityValid = (): boolean => {
    this.logger.trace('isSameCityValid()');
    this.logger.trace('pickupDropoffRequest', this.pickupDropoffRequest);

    if (this.pickupDropoffRequest.pickUpCity && this.pickupDropoffRequest.dropOffCity &&
        this.pickupDropoffRequest.pickUpCity.trim().toLowerCase() === this.pickupDropoffRequest.dropOffCity.trim().toLowerCase()) {
      this.logger.trace('pickup and dropoff cities are the same');
      this.toastrService.error(ValidationMessages.SAME_PICKUP_DROPOFF_CITY, 'Same City Error !');
      return false;
    } else {
      this.logger.trace('pickup and dropoff cities are different');
      return true;
    }
  }

  private isDateTimeValid = (): boolean => {
    this.logger.trace('isDateTimeValid() called');
    const dateValue = this.pickUpDateControl.value;
    const timeValue = this.pickUpTimeControl.value;
    this.logger.trace('dateValue', dateValue, 'timeValue', timeValue);

    if (!dateValue) {
      this.logger.trace('date is invalid!');
      this.toastrService.error(ValidationMessages.VALID_PICKUP_DATE, ValidationMessages.INVALID_PICKUP_DATE);
      return false;
    }

    if (!timeValue) {
      this.logger.trace('time is invalid!');
      this.toastrService.error(ValidationMessages.VALID_START_TIME, ValidationMessages.INVALID_START_TIME);
      return false;
    }

    // Parse time value - could be in HH:MM or HH:MM format from our custom picker
    let hrs: number;
    let mins: number;

    if (timeValue.includes(':')) {
      hrs = this.getHourFromTimeString(timeValue);
      mins = this.getMinuteFromTimeString(timeValue);
    } else {
      this.logger.trace('time format is invalid!');
      this.toastrService.error(ValidationMessages.VALID_START_TIME, ValidationMessages.INVALID_START_TIME);
      return false;
    }

    this.logger.trace('hrs', hrs, 'mins', mins);

    const currentDate = new Date();

    const thresholdLimit = new Date();
    thresholdLimit.setDate(currentDate.getDate() + AppConstants.FUTURE_BOOKING_DAYS_LIMIT);

    // Parse date value - could be in yyyy-mm-dd format
    const requestedDate = new Date(dateValue);

    // Validate if date parsing was successful
    if (isNaN(requestedDate.getTime())) {
      this.logger.trace('date parsing failed!');
      this.toastrService.error(ValidationMessages.VALID_PICKUP_DATE, ValidationMessages.INVALID_PICKUP_DATE);
      return false;
    }

    requestedDate.setHours(hrs);
    requestedDate.setMinutes(mins);

    this.logger.trace('currentDate', currentDate, 'thresholdLimit', thresholdLimit, 'requestedDate', requestedDate);

    if (requestedDate < currentDate) {
      this.logger.trace('failure due to past date');
      this.toastrService.error(ValidationMessages.PAST_PICKUP_DATE_TIME, ValidationMessages.INVALID_PICKUP_DATE_TIME);
      return false;
    }

    if (requestedDate > thresholdLimit) {
      this.logger.trace('failure due to exceeding future date limit');
      this.toastrService.error(ValidationMessages.PICKUP_DATE_LIMIT, ValidationMessages.INVALID_PICKUP_DATE_TIME);
      return false;
    }

    this.logger.trace('date time is valid');
    return true;
  }

  private isCarCategoryValid = (): boolean => {
    this.logger.trace('isCarCategoryValid() called');
    this.logger.trace('selectedCarCategory', this.selectedCarCategory);

    if (!this.selectedCarCategory) {
      this.logger.trace('car category is NOT valid');
      this.toastrService.error(ValidationMessages.SELECT_CAR_CATEGORY, ValidationMessages.CAR_CATEGORY_REQUIRED);
      return false;
    }

    this.logger.trace('car category is valid');
    return true;
  }

  private getHourFromTimeString = (timestring: string): number => {
    this.logger.trace('getHourFromTimeString() called with timestring', timestring);

    // Handle both HH:MM and HH:MM AM/PM formats
    if (timestring.includes(':')) {
      const hrs = timestring.substring(0, timestring.indexOf(':'));
      this.logger.trace('hrs', hrs);
      const hours = parseInt(hrs);
      this.logger.trace('getHourFromTimeString() returning hours', hours);
      return hours;
    }

    return 0;
  }

  private getMinuteFromTimeString = (timestring: string): number => {
    this.logger.trace('getMinuteFromTimeString() called with timestring', timestring);

    // Handle both HH:MM and HH:MM AM/PM formats
    if (timestring.includes(':')) {
      const colonIndex = timestring.indexOf(':');
      let mins: string;

      if (timestring.includes(' ')) {
        // Format: HH:MM AM/PM
        const spaceIndex = timestring.indexOf(' ');
        mins = timestring.substring(colonIndex + 1, spaceIndex);
      } else {
        // Format: HH:MM
        mins = timestring.substring(colonIndex + 1);
      }

      this.logger.trace('mins', mins);
      const minutes = parseInt(mins);
      this.logger.trace('getMinuteFromTimeString() returning minutes', minutes);
      return minutes;
    }

    return 0;
  }

  private navigateBookingPage = () => {
    this.logger.trace('navigateBookingPage() called');
    const params = this.getBookingPageQueryParams();
    this.router.navigate(['/bookingpaymentdeatils'], {queryParams: params});
  }

  private getBookingPageQueryParams = (): Params => {
    this.logger.trace('getBookingPageQueryParams() called');

    this.logger.trace('pickupDropoffRequest', this.pickupDropoffRequest);
    const pickupLngLat = this.pickupDropoffRequest.pickUpAddressLongituted + ',' + this.pickupDropoffRequest.pickUpAddressLatitutde;
    const dropOffLngLat = this.pickupDropoffRequest.dropOffAddressLongituted + ',' + this.pickupDropoffRequest.dropOffAddressLatitutde;

    // TODO Don't hard code pick this from menu
    const tripType = 'One Way';

    const queryParams = {
      [AppConstants.BOOKING_PICKUP_LNG_LAT_QUERY_PARAM] : pickupLngLat,
      [AppConstants.BOOKING_DROPOFF_LNG_LAT_QUERY_PARAM] : dropOffLngLat,
      [AppConstants.BOOKING_TRIP_TYPE_QUERY_PARAM] : tripType,
      [AppConstants.BOOKING_CATEGORY_QUERY_PARAM]: this.selectedCarCategory.categoryName,
      [AppConstants.BOOKING_FROM_ADDRESS_QUERY_PARAM]: this.pickupDropoffRequest.pickUpAddress,
      [AppConstants.BOOKING_TO_ADDRESS_QUERY_PARAM]: this.pickupDropoffRequest.dropOffAddress,
      [AppConstants.BOOKING_PICKUP_CITY_QUERY_PARAM]: this.pickupDropoffRequest.pickUpCity,
      [AppConstants.BOOKING_DROPOFF_CITY_QUERY_PARAM]: this.pickupDropoffRequest.dropOffCity,
      [AppConstants.BOOKING_PICKUP_DATE_QUERY_PARAM]: this.pickupDropoffRequest.bookingDate,
      [AppConstants.BOOKING_PICKUP_TIME_QUERY_PARAM]: this.pickupDropoffRequest.bookingTime
    };

    this.logger.trace('getBookingPageQueryParams() returning queryParams', queryParams);
    return queryParams;
  }


  private showProgressBar = () => {
    this.logger.trace('showProgressBar() called, apiCounter', this.apiCounter);
    if (this.apiCounter === 0) {
      this.logger.trace('Show loading screen');
      this.progresService.isPorgress.next(true);
    }
    this.apiCounter++;
    this.logger.trace('apiCounter incremented', this.apiCounter);
  }

  private hideProgressBar = () => {
    this.logger.trace('hideProgressBar() called, apiCounter', this.apiCounter);
    this.apiCounter--;
    this.logger.trace('apiCounter decremented', this.apiCounter);

    if (this.apiCounter === 0) {
      this.logger.trace('Hide loading screen');
      this.progresService.isPorgress.next(false);
    }
  }

  // Custom Date Picker Methods
  private initCustomDatePicker = () => {
    this.logger.trace('initCustomDatePicker() called');
    if (typeof $ !== 'undefined') {
      $('#customDatepicker').datepicker({
        dateFormat: 'dd-mm-yy',
        duration: 'fast',
        onSelect: (dateText: string) => {
          this.pickUpDateControl.setValue(this.convertDateFormat(dateText));
        }
      });
    }
  }

  private convertDateFormat = (dateText: string): string => {
    // Convert from dd-mm-yy to yyyy-mm-dd for form control
    const parts = dateText.split('-');
    if (parts.length === 3) {
      const day = parts[0];
      const month = parts[1];
      let year = parts[2];

      // Handle both yy and yyyy formats
      if (year.length === 2) {
        year = '20' + year; // Assuming 20xx years
      }

      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }
    return dateText;
  }

  public openDatePicker = () => {
    this.logger.trace('openDatePicker() called');
    if (typeof $ !== 'undefined') {
      $('#customDatepicker').datepicker('show');
    }
  }

  // Custom Time Picker Methods
  private initCustomTimePicker = () => {
    this.logger.trace('initCustomTimePicker() called');
    const timePickableElements = document.querySelectorAll('.time-pickable');

    timePickableElements.forEach((timePickable: any) => {
      timePickable.addEventListener('focus', () => {
        if (this.activePicker) { return; }

        this.activePicker = this.showTimePicker(timePickable);

        const onClickAway = ({ target }: any) => {
          if (
            target === this.activePicker ||
            target === timePickable ||
            this.activePicker.contains(target)) {
            return;
          }

          document.removeEventListener('mousedown', onClickAway);
          if (this.activePicker && this.activePicker.parentNode) {
            document.body.removeChild(this.activePicker);
          }
          this.activePicker = null;
        };

        document.addEventListener('mousedown', onClickAway);
      });
    });
  }

  public openTimePicker = () => {
    this.logger.trace('openTimePicker() called');
    const timePickerInput = document.getElementById('customTimepicker') as HTMLInputElement;
    if (timePickerInput) {
      timePickerInput.focus();
    }
  }

  private showTimePicker = (timePickable: any) => {
    this.logger.trace('showTimePicker called');
    const picker = this.buildTimePicker(timePickable);
    const rect = timePickable.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    picker.style.position = 'absolute';
    picker.style.top = `${rect.bottom + scrollTop + 5}px`;
    picker.style.left = `${rect.left + scrollLeft}px`;
    picker.style.zIndex = '1000';
    picker.style.display = 'block';
    picker.style.visibility = 'visible';

    document.body.appendChild(picker);
    this.logger.trace('Time picker added to DOM at position:', picker.style.top, picker.style.left);
    this.logger.trace('Time picker element:', picker);

    return picker;
  }

  private buildTimePicker = (timePickable: any) => {
    this.logger.trace('buildTimePicker called');
    const picker = document.createElement('div');
    const hourOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(this.numberToOption);
    const minuteOptions = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55].map(this.numberToOption);

    picker.classList.add('time-picker');
    picker.innerHTML = `
      <select class="time-picker__select">
        ${hourOptions.join('')}
      </select>
      <span style="margin: 0 5px; font-weight: bold; color: #546E7A;">:</span>
      <select class="time-picker__select">
        ${minuteOptions.join('')}
      </select>
      <select class="time-picker__select">
        <option value="am">am</option>
        <option value="pm">pm</option>
      </select>
    `;

    this.logger.trace('Time picker HTML created:', picker.innerHTML);
    const selects = this.getSelectsFromPicker(picker);

    selects.hour.addEventListener('change', () => {
      const timeString = this.getTimeStringFromPicker(picker);
      timePickable.value = timeString;
      this.pickUpTimeControl.setValue(this.convertTimeToHHMM(timeString));
    });

    selects.minute.addEventListener('change', () => {
      const timeString = this.getTimeStringFromPicker(picker);
      timePickable.value = timeString;
      this.pickUpTimeControl.setValue(this.convertTimeToHHMM(timeString));
    });

    selects.meridiem.addEventListener('change', () => {
      const timeString = this.getTimeStringFromPicker(picker);
      timePickable.value = timeString;
      this.pickUpTimeControl.setValue(this.convertTimeToHHMM(timeString));
    });

    if (timePickable.value) {
      const { hour, minute, meridiem } = this.getTimePartsFromPickable(timePickable);
      selects.hour.value = hour;
      selects.minute.value = minute;
      selects.meridiem.value = meridiem;
    }

    return picker;
  }

  private convertTimeToHHMM = (timeString: string): string => {
    // Convert from "1:30 pm" to "13:30" format
    const parts = timeString.split(' ');
    if (parts.length === 2) {
      const timePart = parts[0];
      const meridiem = parts[1];
      const [hour, minute] = timePart.split(':');

      let hour24 = parseInt(hour);
      if (meridiem === 'pm' && hour24 !== 12) {
        hour24 += 12;
      } else if (meridiem === 'am' && hour24 === 12) {
        hour24 = 0;
      }

      return `${hour24.toString().padStart(2, '0')}:${minute}`;
    }
    return timeString;
  }

  private getTimePartsFromPickable = (timePickable: any) => {
    const pattern = /^(\d+):(\d+) (am|pm)$/;
    const match = timePickable.value.match(pattern);

    if (match) {
      const [, hour, minute, meridiem] = match;
      return { hour, minute, meridiem };
    }

    return { hour: '01', minute: '00', meridiem: 'am' };
  }

  private getSelectsFromPicker = (timePicker: any) => {
    const [hour, minute, meridiem] = timePicker.querySelectorAll('.time-picker__select');
    return { hour, minute, meridiem };
  }

  private getTimeStringFromPicker = (timePicker: any) => {
    const selects = this.getSelectsFromPicker(timePicker);
    return `${selects.hour.value}:${selects.minute.value} ${selects.meridiem.value}`;
  }

  private numberToOption = (number: number) => {
    const padded = number.toString().padStart(2, '0');
    return `<option value="${padded}">${padded}</option>`;
  }

  public openWhatsApp = () => {
    this.logger.trace('openWhatsApp() called');

    // Check if we have the required data
    if (!this.pickupDropoffRequest || !this.selectedCarCategory) {
      this.toastrService.error('Please select pickup/dropoff locations and car category first', 'Missing Information');
      return;
    }

    const whatsappNumber = '************'; // Adding country code 91 for India
    const message = this.generateWhatsAppMessage();
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodedMessage}`;

    // Open WhatsApp in a new window
    window.open(whatsappUrl, '_blank');
  }

  private generateWhatsAppMessage = (): string => {
    this.logger.trace('generateWhatsAppMessage() called');

    const fromLocation = this.pickupDropoffRequest.pickUpAddress || this.pickupDropoffRequest.pickUpCity || 'Not specified';
    const toLocation = this.pickupDropoffRequest.dropOffAddress || this.pickupDropoffRequest.dropOffCity || 'Not specified';

    const message = `Hi! I need a cab from *${fromLocation}* to *${toLocation}*. Please share availability and pricing details. Thanks!`;

    this.logger.trace('Generated WhatsApp message:', message);
    return message;
  }
}
