import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, startWith, debounceTime } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { City } from '../../models/city_model';
import { CompanyGenericService } from '../../services/company-generic.service';
import { CityRequest } from '../../models/city-request.model';
import { ValidationMessages } from '../../constants/message.content';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CityRequestSharedService } from '../../services/city-request-shared.service';
import { AppConstants } from '../../constants/AppConstants';
import { GeolocationService, GeolocationPosition } from '../../services/geolocation.service';
import { MmiService } from '../../services/mmi/mmi-service.service';
import { GoogleMapsService } from '../../services/google-maps/google-maps.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']

})
export class HeaderComponent implements OnInit {

  constructor(private cityRequestSharedService: CityRequestSharedService,
              private router: Router,
              private toastr: ToastrService,
              private companyGenericService: CompanyGenericService,
              private geolocationService: GeolocationService,
              private mmiService: MmiService,
              private googleMapsService: GoogleMapsService) {
  }

  @Input() imageFolderPath: string;

  @Output() setCityRequestEvent: any = new EventEmitter<any>();
  @Output() setSideMenuVisibleEvent: any = new EventEmitter<any>();

  pickUpCityControl = new FormControl();
  dropOffCityControl = new FormControl();
  
  pickUpCity: string[] = [];
  dropOffCity: string[] = [];

  private pickupCityMap: Map<string, City>;
  private dropOffCityMap: Map<string, City>;

  private selectedPickupCity: City;
  private selectedDropOffCity: City

  private cityRequest: CityRequest = {
    pickUpCity: '', 
    pickUpCityLatitutde: '',
    pickUpCityLongituted: '',
    dropOffCity: '',
    dropOffCityLatitutde: '',
    dropOffCityLongituted: '',
    pickUpCityEloc:'',
    dropOffCityEloc:''
  };

  filteredOptionsPickUpCity: Observable<string[]>;
  filteredOptionsDropOffCity: Observable<string[]>;

  // Current location properties
  private token: string;
  private hasAttemptedLocationDetection: boolean = false;

  ngOnInit() {
    this.resetCityMap();
    this.initializeMMIToken();

    this.cityRequestSharedService.sharedInformations.subscribe(cityRequest => this.cityRequest = cityRequest);

    this.filteredOptionsPickUpCity = this.pickUpCityControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(AppConstants.AUTO_SUGGEST_DEBOUNCE_MILLSECONDS),
        map(value => {
          // Reset selected city if user types something different
          if (this.selectedPickupCity && value !== this.selectedPickupCity.cityName) {
            this.selectedPickupCity = null;
          }
          return this._filterPickUpCity(value);
        })
      );

    this.filteredOptionsDropOffCity = this.dropOffCityControl.valueChanges
      .pipe(
        startWith(''),
        debounceTime(AppConstants.AUTO_SUGGEST_DEBOUNCE_MILLSECONDS),
        map(value => {
          // Reset selected city if user types something different
          if (this.selectedDropOffCity && value !== this.selectedDropOffCity.cityName) {
            this.selectedDropOffCity = null;
          }
          return this._filterDropOffCity(value);
        })
      );

    // Automatically attempt location detection on page load
    this.attemptAutoLocationDetection();
  }

  private resetCityMap = () => {
    this.resetPickupCityMap();
    this.resetDropOffCityMap();
  }

  private resetPickupCityMap = () => {
    this.pickupCityMap = new Map();
  }

  private resetDropOffCityMap = () => {
    this.dropOffCityMap = new Map();
  }

  private setPickupCityMap = (city: City) => {
    this.pickupCityMap.set(city.cityName, city);
  }

  private setDropOffCityMap = (city: City) => {
    this.dropOffCityMap.set(city.cityName, city);
  }

  private _filterPickUpCity(value: string): string[] {
    const filterValue = value.toLowerCase();

    this.pickUpCity = [];
    this.resetPickupCityMap;

    this.companyGenericService.getCities().subscribe(
      (response: Array<City>) => {
        // let res = response as Array<City>;
        // this.pickUpCity.length = 0;

        const pickUpCityNames = response.filter(option => option.cityName.toLowerCase().includes(filterValue));
        pickUpCityNames.forEach(element => {
          this.setPickupCityMap(element);
          this.pickUpCity.push(element.cityName);
        });

      }, (error: HttpErrorResponse) => {
        console.log(error.error);
      }
    );

    // I don't think this return statement will do anthing, above code in aynschronous
    return this.pickUpCity;
    //return this.options.filter(option => option.toLowerCase().includes(filterValue));
  }

  public _filterDropOffCity(value: string): string[] {
    const filterValue = value.toLowerCase();

    this.dropOffCity = [];
    this.resetDropOffCityMap();

    this.companyGenericService.getCities().subscribe(
      (response: Array<City>) => {
        // var res = response as Array<City>;
        // this.dropOffCity.length = 0;
        let dropOffCityNames = response.filter(option => option.cityName.toLowerCase().includes(filterValue));
        dropOffCityNames.forEach(element => {
          this.setDropOffCityMap(element);
          this.dropOffCity.push(element.cityName);
        });

      }, (error: HttpErrorResponse) => {
        console.log(error.error);
      }

    );

    // I don't think this return statement will do anthing, above code in aynschronous
    return this.dropOffCity;
  }

  public _filterPickUpCityLatLong(event): void {
    // const filterValue = event.option.value.toLowerCase();
    const filterValue = event.option.value;
    const city = this.pickupCityMap.get(filterValue);
    this.selectedPickupCity = city;
  }

  public _filterDropOffCityLatLong(event): void {
    // const filterValue = event.option.value.toLowerCase();
    const filterValue = event.option.value;
    const city = this.dropOffCityMap.get(filterValue);
    this.selectedDropOffCity = city;
  }

  InputSwipe() {
    console.log('InputSwipe() called');
    console.log('Before swipe');
    console.log('selectedPickupCity', this.selectedPickupCity);
    console.log('selectedDropOffCity', this.selectedDropOffCity);

    const temp = this.selectedPickupCity;
    this.selectedPickupCity = this.selectedDropOffCity;
    this.selectedDropOffCity = temp;
    
    console.log('After swipe');
    console.log('selectedPickupCity', this.selectedPickupCity);
    console.log('selectedDropOffCity', this.selectedDropOffCity);

    this.pickUpCityControl.setValue(this.selectedPickupCity.cityName);
    this.dropOffCityControl.setValue(this.selectedDropOffCity.cityName);
  }

  ValidateInputs() {
    if (this.pickUpCityControl.value === '' || this.pickUpCityControl.value === null) {
      this.toastr.error(ValidationMessages.PICK_UP_CITY,'City Name Required !');
    } else if (this.dropOffCityControl.value === '' || this.dropOffCityControl.value === null) {
      this.toastr.error(ValidationMessages.DROP_OFF_CITY,'City Name Required !');
    } else if (!this.selectedPickupCity) {
      this.toastr.error(ValidationMessages.INVALID_PICKUP_CITY_SELECTION,'Invalid City Selection !');
    } else if (!this.selectedDropOffCity) {
      this.toastr.error(ValidationMessages.INVALID_DROPOFF_CITY_SELECTION,'Invalid City Selection !');
    } else if (this.pickUpCityControl.value === this.dropOffCityControl.value) {
      this.toastr.error(ValidationMessages.SAME_PICKUP_DROPOFF_CITY,'Same City Error !');
    } else {
      this.createCityRequest();

      this.setCityRequestEvent.emit(this.cityRequest);
      this.setSideMenuVisibleEvent.emit(false);
      this.cityRequestSharedService.nextData(this.cityRequest);

      /* const tripCityQueryParam = this.getTripCityQueryParam();
      this.router.navigate(['/pickupdropoffdetails'],{ queryParams: { tripcity: tripCityQueryParam} }); */

      const routeUrl = this.getPickupDropOffPageRouteUrl();
      this.router.navigate([routeUrl]);
    }
  }

  private createCityRequest = () => {
    console.log('createCityRequest() called');
    console.log('selectedPickupCity', this.selectedPickupCity);
    console.log('selectedDropOffCity', this.selectedDropOffCity);

    const request = new CityRequest();

    request.pickUpCity = this.selectedPickupCity.cityName;
    request.pickUpCityEloc = this.selectedPickupCity.eLoc;
    request.pickUpCityLatitutde = this.selectedPickupCity.latitude;
    request.pickUpCityLongituted = this.selectedPickupCity.longitutde;

    request.dropOffCity = this.selectedDropOffCity.cityName;
    request.dropOffCityEloc = this.selectedDropOffCity.eLoc;
    request.dropOffCityLatitutde = this.selectedDropOffCity.latitude;
    request.dropOffCityLongituted = this.selectedDropOffCity.longitutde;

    this.cityRequest = request;
    console.log('createCityRequest() created request', this.cityRequest);
  }

  /* private getTripCityQueryParam = (): string => {
    console.log('getTripCityQueryParam() called');
    console.log('selectedPickupCity', this.selectedPickupCity);
    console.log('selectedDropOffCity', this.selectedDropOffCity);

    const pickupCity: string = this.selectedPickupCity.cityName;
    const dropOffCity: string = this.selectedDropOffCity.cityName;
    const queryParam: string = pickupCity + '-' + dropOffCity

    console.log('getTripCityQueryParam() returning queryParam', queryParam);
    return queryParam;
  } */

  private getPickupDropOffPageRouteUrl = (): string => {
    console.log('getPickupDropOffPageRouteUrl() called');
    console.log('selectedPickupCity', this.selectedPickupCity);
    console.log('selectedDropOffCity', this.selectedDropOffCity);

    const pickupCity: string = this.selectedPickupCity.cityName;
    const dropOffCity: string = this.selectedDropOffCity.cityName;
    const pathParam: string = pickupCity + '-to-' + dropOffCity

    const routeUrl = `/pickupdropoffdetails/${pathParam}`;

    console.log('getTripCityQueryParam() returning routeUrl', routeUrl);
    return routeUrl;
  }

  // Geolocation methods
  private initializeMMIToken = () => {
    this.mmiService.getToken().then((data: any) => {
      this.token = data['access_token'];
      console.log('MMI token initialized', this.token);
    }).catch(error => {
      console.error('Failed to get MMI token', error);
    });
  }

  private attemptAutoLocationDetection = () => {
    // Only attempt once per session and if geolocation is supported
    if (this.hasAttemptedLocationDetection || !this.geolocationService.isGeolocationSupported()) {
      return;
    }

    this.hasAttemptedLocationDetection = true;

    // Silently attempt to get location without showing loading indicators
    this.geolocationService.getCurrentPosition().subscribe(
      (position: GeolocationPosition) => {
        console.log('Auto-detected current position:', position);
        this.reverseGeocodeAndSelectCity(position.latitude, position.longitude);
      },
      (error) => {
        // Silently handle errors - user can manually select cities
        console.log('Auto location detection failed (this is normal):', error);
      }
    );
  }

  private reverseGeocodeAndSelectCity = (lat: number, lng: number) => {
    // Try Google Maps reverse geocoding first
    this.googleMapsService.reverseGeocode(lat, lng).subscribe(
      (response: any) => {
        console.log('Google Maps Reverse geocoding response:', response);

        // Extract city name from response
        const cityName = response.city;
        if (cityName) {
          this.autoSelectCityIfAvailable(cityName);
        } else {
          this.tryFallbackReverseGeocode(lat, lng);
        }
      },
      (error) => {
        console.log('Google Maps Reverse geocoding error, trying fallback:', error);
        // Fallback to public geocoding service
        this.tryFallbackReverseGeocode(lat, lng);
      }
    );
  }

  private tryFallbackReverseGeocode = (lat: number, lng: number) => {
    // Use a public reverse geocoding service as fallback
    const fallbackUrl = `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`;

    fetch(fallbackUrl)
      .then(response => response.json())
      .then(data => {
        console.log('Fallback reverse geocoding response:', data);

        const cityName = data.city || data.locality || data.principalSubdivision;
        if (cityName) {
          this.autoSelectCityIfAvailable(cityName);
        } else {
          console.log('Could not determine city from location data');
        }
      })
      .catch(error => {
        console.log('Fallback reverse geocoding failed:', error);
      });
  }

  private extractCityFromReverseGeocode = (response: any): string | null => {
    // This method needs to be adapted based on the actual response structure from MMI reverse geocoding
    // For now, we'll try common response structures
    try {
      if (response && response.results && response.results.length > 0) {
        const result = response.results[0];
        return result.city || result.district || result.locality || null;
      }

      if (response && response.city) {
        return response.city;
      }

      if (response && response.formatted_address) {
        // Try to extract city from formatted address
        const parts = response.formatted_address.split(',');
        if (parts.length > 1) {
          return parts[1].trim();
        }
      }
    } catch (error) {
      console.error('Error extracting city from reverse geocode response:', error);
    }

    return null;
  }

  private autoSelectCityIfAvailable = (cityName: string) => {
    // Get all available cities and check if the detected city matches any
    this.companyGenericService.getCities().subscribe(
      (cities: Array<City>) => {
        const matchingCity = cities.find(city =>
          city.cityName.toLowerCase() === cityName.toLowerCase() ||
          city.cityName.toLowerCase().includes(cityName.toLowerCase()) ||
          cityName.toLowerCase().includes(city.cityName.toLowerCase())
        );

        if (matchingCity) {
          // Auto-select the matching city in pickup field
          this.selectedPickupCity = matchingCity;
          this.pickUpCityControl.setValue(matchingCity.cityName);
          this.setPickupCityMap(matchingCity);

          // Show subtle success message
          this.toastr.success(`Auto-detected: ${matchingCity.cityName}`, 'Current Location', {
            timeOut: 3000,
            positionClass: 'toast-bottom-right'
          });

          console.log(`Auto-selected ${matchingCity.cityName} based on current location`);
        } else {
          console.log(`${cityName} is not available in service areas`);
        }
      },
      (error) => {
        console.error('Error fetching cities for auto-selection:', error);
      }
    );
  }
}
