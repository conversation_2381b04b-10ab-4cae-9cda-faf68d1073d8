import { PaymentResponseService } from './../../services/payment-response.service';
import { BookingRequest } from './../../models/booking.model';
import { BookingReceiptData, BookingReceiptResponse } from './../../models/booking-receipt.model';
import { BookingAPIService } from './../../services/booking-api.service';
import { AppConfig } from 'src/app/configs/app.config';
import { Router, ActivatedRoute } from '@angular/router';
import { LoggerService } from './../../interceptors/logger.service';
import { Component, OnInit, OnDestroy, AfterViewInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { MmiService } from '../../services/mmi/mmi-service.service';
import { GoogleMapsService } from '../../services/google-maps/google-maps.service';
import { ETA } from '../../models/mmi/mmt_eta_model';
import { PolyLineDecoderService } from '../../services/poyline-decoder.service';
import { LocationCoordinate } from '../../models/location_coordinate_model';

declare var google: any;

@Component({
  selector: 'app-confirmation-page',
  templateUrl: './confirmation-page.component.html',
  styleUrls: ['./confirmation-page.component.css']
})
export class ConfirmationPageComponent implements OnInit, OnDestroy, AfterViewInit {
  private logger: LoggerService;
  private routeSubscription: Subscription;
  private bookingSubscription: Subscription;
  private etaSubscription: Subscription;

  // Map related properties
  private map: any;
  private startMarker: any;
  private endMarker: any;
  private routePolyline: any;

  bookingId: string;
  bookingRequest: BookingRequest;
  bookingData: BookingReceiptData;
  paymentData: any;
  imageFolderPath: string = AppConfig.imageFolderPath;
  isLoading: boolean = true;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private paymentResponse: PaymentResponseService,
    private paymentRepsponseService: PaymentResponseService,
    private bookingAPIService: BookingAPIService,
    private mmiService: MmiService,
    private polyLineDecoderService: PolyLineDecoderService) {
    this.logger = LoggerService.createLogger('ConfirmationPageComponent');
  }

  ngOnInit(): void {
    this.logger.debug('ConfirmationPageComponent ngOnInit called');

    // Get payment response data
    this.paymentData = this.paymentResponse.paymentResponseValue?.data;
    this.bookingRequest = this.paymentRepsponseService.bookingRequestData;

    // Get booking ID from route parameter
    this.routeSubscription = this.route.params.subscribe(params => {
      this.bookingId = params['id'];
      this.logger.debug('Booking ID from route:', this.bookingId);

      if (this.bookingId) {
        this.fetchBookingDetails();
      } else {
        this.logger.error('No booking ID found in route');
        this.isLoading = false;
      }
    });
  }

  ngAfterViewInit(): void {
    // Initialize map after view is ready
    setTimeout(() => {
      this.initializeMap();
    }, 100);
  }

  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.bookingSubscription) {
      this.bookingSubscription.unsubscribe();
    }
    if (this.etaSubscription) {
      this.etaSubscription.unsubscribe();
    }
  }

  private fetchBookingDetails(): void {
    this.isLoading = true;
    this.bookingSubscription = this.bookingAPIService.getBookingById(this.bookingId).subscribe(
      (response: any) => {
        this.isLoading = false;
        this.logger.debug('Booking details response:', response);

        if (response.succeeded && response.data) {
          this.bookingData = this.mapApiResponseToBookingData(response.data);
          // Initialize map after booking data is loaded
          setTimeout(() => {
            if (this.map) {
              this.drawRouteOnMap();
            }
          }, 500);
        } else {
          this.logger.error('Failed to fetch booking details:', response.message);
          // Fallback to payment response data if available
          this.usePaymentResponseData();
        }
      },
      (error) => {
        this.isLoading = false;
        this.logger.error('Error fetching booking details:', error);
        // Fallback to payment response data if available
        this.usePaymentResponseData();
      }
    );
  }

  private usePaymentResponseData(): void {
    // Use data from payment response and booking request as fallback
    if (this.bookingRequest && this.paymentData) {
      this.bookingData = {
        bookingId: this.bookingId,
        tripType: this.bookingRequest.tripType,
        pickUpCity: this.bookingRequest.pickUpCity,
        dropOffCity: this.bookingRequest.dropOffCity,
        pickUpAddress: this.bookingRequest.pickUpAddress,
        dropOffAddress: this.bookingRequest.dropOffAddress,
        pickUpDate: this.bookingRequest.pickUpDate,
        pickUpTime: this.bookingRequest.pickUpTime,
        carCategory: this.bookingRequest.carCategory,
        carFeatures: this.bookingRequest.carFeatures,
        carCapacity: this.bookingRequest.carCapacity,
        distance: this.bookingRequest.distance,
        duration: this.bookingRequest.duration,
        basicFare: this.bookingRequest.basicFare,
        gst: this.bookingRequest.gst,
        totalFare: this.paymentData.amount || this.bookingRequest.fare,
        paymentType: this.paymentData.paymentType || 'UNKNOWN',
        paymentStatus: this.paymentData.paymentStatus || 'Paid',
        amountPaid: this.paymentData.amount || this.bookingRequest.fare,
        remainingAmountForDriver: this.paymentData.remainingAmountForDriver || 0,
        travelerName: this.bookingRequest.travelerName,
        phoneNumber: this.bookingRequest.phoneNumber,
        mailId: this.bookingRequest.mailId
      };
      // Initialize map after booking data is loaded
      setTimeout(() => {
        if (this.map) {
          this.drawRouteOnMap();
        }
      }, 500);
    }
  }

  // Map API response to our expected format
  private mapApiResponseToBookingData(apiData: any): BookingReceiptData {
    // Calculate correct total fare
    const calculatedTotalFare = apiData.basicFare + apiData.gst;

    // Determine if this is a partial payment based on paymentOption
    const isPartialPayment = apiData.paymentOption === 1;

    // Calculate correct remaining amount for driver
    let remainingAmountForDriver = 0;
    if (isPartialPayment) {
      // For partial payments, always use cashAmountToPayDriver as it's the correct value
      remainingAmountForDriver = apiData.cashAmountToPayDriver || 0;
    } else {
      // For full payments, should be 0
      remainingAmountForDriver = 0;
    }

    // Determine correct payment type
    const paymentType = isPartialPayment ? 'PARTIAL' : 'FULL';

    return {
      bookingId: apiData.bookingID, // Note: API returns bookingID, not bookingId
      tripType: apiData.tripType,
      pickUpCity: apiData.pickUpCity,
      dropOffCity: apiData.dropOffCity,
      pickUpAddress: apiData.pickUpAddress,
      dropOffAddress: apiData.dropOffAddress,
      pickUpDate: apiData.pickUpDate || this.bookingRequest?.pickUpDate || 'Not specified',
      pickUpTime: apiData.pickUpTime || this.bookingRequest?.pickUpTime || 'Not specified',
      carCategory: apiData.carCategory,
      carFeatures: '', // Remove car features as requested
      carCapacity: this.bookingRequest?.carCapacity || 0, // Not in API response
      distance: apiData.distance,
      duration: apiData.duration,
      basicFare: apiData.basicFare,
      gst: apiData.gst,
      totalFare: calculatedTotalFare, // Use calculated total fare (basicFare + gst)
      paymentType: paymentType, // Use corrected payment type
      paymentStatus: apiData.paymentStatus || apiData.razorpayStatus || 'Paid',
      amountPaid: apiData.fare, // Amount paid online
      remainingAmountForDriver: remainingAmountForDriver, // Corrected remaining amount
      travelerName: apiData.travelerName,
      phoneNumber: apiData.phoneNumber,
      mailId: apiData.mailId
    };
  }

  calculateDropOffTime(): string {
    const pickUpTime = this.bookingData?.pickUpTime || this.bookingRequest?.pickUpTime;
    const duration = this.bookingData?.duration || this.bookingRequest?.duration;

    if (!pickUpTime || !duration) {
      return 'Not specified';
    }

    try {
      // Parse pickup time (format: "HH:MM")
      const [hours, minutes] = pickUpTime.split(':').map(Number);

      // Parse duration (format: "X hrs Y mins" or similar)
      const durationMatch = duration.match(/(\d+)\s*hrs?\s*(\d+)?\s*mins?/i);
      if (!durationMatch) {
        return pickUpTime; // Return original time if duration parsing fails
      }

      const durationHours = parseInt(durationMatch[1]) || 0;
      const durationMinutes = parseInt(durationMatch[2]) || 0;

      // Calculate drop-off time
      const pickUpDate = new Date();
      pickUpDate.setHours(hours, minutes, 0, 0);

      const dropOffDate = new Date(pickUpDate);
      dropOffDate.setHours(dropOffDate.getHours() + durationHours);
      dropOffDate.setMinutes(dropOffDate.getMinutes() + durationMinutes);

      // Format as HH:MM
      const dropOffHours = dropOffDate.getHours().toString().padStart(2, '0');
      const dropOffMinutes = dropOffDate.getMinutes().toString().padStart(2, '0');

      return `${dropOffHours}:${dropOffMinutes}`;
    } catch (error) {
      this.logger.error('Error calculating drop-off time:', error);
      return pickUpTime; // Return original time if calculation fails
    }
  }

  private initializeMap(): void {
    try {
      this.logger.debug('Initializing map...');
      this.map = new MapmyIndia.Map('confirmation-map', {
        center: [28.04, 78.2], // Default center (Delhi)
        zoom: 10
      });

      // Wait for booking data to be loaded before drawing route
      if (this.bookingData) {
        this.drawRouteOnMap();
      }
    } catch (error) {
      this.logger.error('Error initializing map:', error);
    }
  }

  private drawRouteOnMap(): void {
    if (!this.map || !this.bookingData) {
      return;
    }

    try {
      // Extract coordinates from pickup and dropoff addresses
      const pickupCoords = this.extractCoordinatesFromAddress(this.bookingData.pickUpAddress);
      const dropoffCoords = this.extractCoordinatesFromAddress(this.bookingData.dropOffAddress);

      if (pickupCoords && dropoffCoords) {
        this.addMarkersAndRoute(pickupCoords, dropoffCoords);
      } else {
        this.logger.error('Could not extract coordinates from addresses');
        // Fallback: try to get coordinates from city names
        this.drawRouteFromCityNames();
      }
    } catch (error) {
      this.logger.error('Error drawing route on map:', error);
    }
  }

  private extractCoordinatesFromAddress(address: string): { lat: number, lng: number } | null {
    // This is a simple extraction - in a real app, you'd use geocoding
    // For now, we'll return null to trigger the city-based fallback
    return null;
  }

  private drawRouteFromCityNames(): void {
    // Default coordinates for major cities (you can expand this)
    const cityCoordinates = {
      'Mumbai': { lat: 19.0760, lng: 72.8777 },
      'Delhi': { lat: 28.7041, lng: 77.1025 },
      'Bangalore': { lat: 12.9716, lng: 77.5946 },
      'Pune': { lat: 18.5204, lng: 73.8567 },
      'Hyderabad': { lat: 17.3850, lng: 78.4867 },
      'Chennai': { lat: 13.0827, lng: 80.2707 },
      'Kolkata': { lat: 22.5726, lng: 88.3639 },
      'Ahmedabad': { lat: 23.0225, lng: 72.5714 },
      'Jaipur': { lat: 26.9124, lng: 75.7873 },
      'Surat': { lat: 21.1702, lng: 72.8311 }
    };

    const pickupCity = this.bookingData.pickUpCity;
    const dropoffCity = this.bookingData.dropOffCity;

    const pickupCoords = cityCoordinates[pickupCity];
    const dropoffCoords = cityCoordinates[dropoffCity];

    if (pickupCoords && dropoffCoords) {
      this.addMarkersAndRoute(pickupCoords, dropoffCoords);
    } else {
      this.logger.error('City coordinates not found for', pickupCity, 'or', dropoffCity);
    }
  }

  private addMarkersAndRoute(pickupCoords: { lat: number, lng: number }, dropoffCoords: { lat: number, lng: number }): void {
    // Add pickup marker
    this.startMarker = new L.marker([pickupCoords.lat, pickupCoords.lng], {
      title: 'Pickup Location'
    }).addTo(this.map);
    this.startMarker.bindPopup(`<b>Pickup:</b><br>${this.bookingData.pickUpCity}`, {
      closeButton: true,
      autopan: true
    });

    // Add dropoff marker
    this.endMarker = new L.marker([dropoffCoords.lat, dropoffCoords.lng], {
      title: 'Drop-off Location'
    }).addTo(this.map);
    this.endMarker.bindPopup(`<b>Drop-off:</b><br>${this.bookingData.dropOffCity}`, {
      closeButton: true,
      autopan: true
    });

    // Fit map to show both markers
    const bounds = new L.LatLngBounds([pickupCoords.lat, pickupCoords.lng], [dropoffCoords.lat, dropoffCoords.lng]);
    this.map.fitBounds(bounds, { padding: [20, 20] });

    // Get and draw route
    this.getAndDrawRoute(pickupCoords, dropoffCoords);
  }

  private getAndDrawRoute(pickupCoords: { lat: number, lng: number }, dropoffCoords: { lat: number, lng: number }): void {
    this.etaSubscription = this.mmiService.getETA(
      pickupCoords.lat.toString(),
      pickupCoords.lng.toString(),
      dropoffCoords.lat.toString(),
      dropoffCoords.lng.toString()
    ).subscribe(
      (response: ETA) => {
        this.logger.debug('ETA response:', response);
        const routes = response.routes;
        if (routes && routes.length > 0) {
          const firstRoute = routes[0];
          if (firstRoute && firstRoute.geometry) {
            const encodedPolyline = firstRoute.geometry;
            const polyline = this.polyLineDecoderService.decode(encodedPolyline);
            this.createMapPolyline(polyline);
          }
        }
      },
      (error) => {
        this.logger.error('Error getting route:', error);
        // Draw a simple straight line as fallback
        this.drawStraightLine(pickupCoords, dropoffCoords);
      }
    );
  }

  private createMapPolyline(route: LocationCoordinate[]): void {
    this.logger.debug('Creating polyline with route:', route);

    if (this.routePolyline) {
      this.map.removeLayer(this.routePolyline);
    }

    const pathArray = [];
    route.forEach(point => pathArray.push(new L.LatLng(point.latitude, point.longitude)));

    this.routePolyline = new L.Polyline(pathArray, {
      color: '#2196F3',
      weight: 5,
      opacity: 0.8,
      smoothFactor: 1
    }).addTo(this.map);
  }

  private drawStraightLine(pickupCoords: { lat: number, lng: number }, dropoffCoords: { lat: number, lng: number }): void {
    if (this.routePolyline) {
      this.map.removeLayer(this.routePolyline);
    }

    const pathArray = [
      new L.LatLng(pickupCoords.lat, pickupCoords.lng),
      new L.LatLng(dropoffCoords.lat, dropoffCoords.lng)
    ];

    this.routePolyline = new L.Polyline(pathArray, {
      color: '#FF5722',
      weight: 3,
      opacity: 0.7,
      dashArray: '10, 10'
    }).addTo(this.map);
  }

  navigateToHome = () => {
    this.router.navigate(['home']);
  }
}
